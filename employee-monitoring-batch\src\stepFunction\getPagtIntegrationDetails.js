// Require common library to access common function
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { getConnection, convertObjectKeyToAnotherKeyValue,
    updateContactInfo, updatePersonalInfo, initiateCustomGroupRefresh } = require("./commonFunctions");
const axios = require('axios');
// Require knex to make DB connection
const knex = require('knex');
// Require table alias
const { ehrTables } = require('../common/tableAlias');
const moment = require('moment-timezone');

// Constants for better maintainability
const TRIGGER_TYPES = {
    MASTER: 'Master',
    EMPLOYEE: 'Employee',
    ONBOARD_STATUS: 'OnboardStatus'
};

const API_ENDPOINTS = {
    ORGANIZATION_SETTINGS: '/entomo/sync-organizationsettinglist-to-entomo',
    EMPLOYEE_TYPES: '/entomo/sync-employeetypelist-to-entomo',
    GRADES: '/entomo/sync-gradelist-to-entomo',
    DESIGNATIONS: '/entomo/sync-designationlist-to-entomo',
    LOCATIONS: '/entomo/sync-locationlist-to-entomo',
    EMPLOYEE_INFO: '/entomo/sync-employeeinfo-changes-to-entomo',
    EMPLOYEE_ADDITIONAL_INFO: '/entomo/sync-employeeAdditionalinfo-changes-to-entomo',
    ONBOARDING_STATUS: '/entomo/sync-onboarding-status-details-to-staging'
};

/**
 * Main function to handle PAGT integration details synchronization
 * @param {Object} event - AWS Lambda event object
 * @param {Object} context - AWS Lambda context object
 * @returns {Object} Response object with next step information
 */
module.exports.getPagtIntegrationDetails = async (event, context) => {
    let appmanagerDbConnection, organizationDbConnection;
    let triggerType;

    try {
        console.log('Starting getPagtIntegrationDetails with event:', JSON.stringify(event, null, 2));

        // Input validation and extraction
        const validationResult = validateAndExtractInput(event);
        if (!validationResult.isValid) {
            throw new Error(`Input validation failed: ${validationResult.error}`);
        }

        triggerType = validationResult.triggerType;
        const frequency = validationResult.frequency;

        // Establish database connection
        const databaseConnection = await establishDatabaseConnection();
        if (!databaseConnection) {
            throw new Error('Failed to establish database connection');
        }

        appmanagerDbConnection = knex(databaseConnection.AppManagerDb);

        // Get active organization instances
        const orgCodeActiveInstances = await getOrgActiveInstances(appmanagerDbConnection, triggerType);

        if (!orgCodeActiveInstances || orgCodeActiveInstances.length === 0) {
            console.log(`No active instances found for trigger type: ${triggerType}`);
            return buildResponse(triggerType, 'No active instances found');
        }

        // Process each organization instance
        await processOrganizationInstances(
            orgCodeActiveInstances,
            appmanagerDbConnection,
            triggerType
        );

        return buildResponse(triggerType, 'Process completed successfully');

    } catch (error) {
        console.error("Error in getPagtIntegrationDetails main function:", error);
        await handleMainError(error, triggerType, appmanagerDbConnection, organizationDbConnection);
        return buildErrorResponse(triggerType, error.message);
    } finally {
        // Ensure database connections are properly closed
        await closeDbConnection(appmanagerDbConnection, 'AppManager');
        await closeDbConnection(organizationDbConnection, 'Organization');
    }
};

/**
 * Validates and extracts input parameters from the event
 * @param {Object} event - AWS Lambda event object
 * @returns {Object} Validation result with extracted parameters
 */
function validateAndExtractInput(event) {
    try {
        if (!event) {
            return { isValid: false, error: 'Event object is required' };
        }

        const triggerType = event.input?.source || event.source || TRIGGER_TYPES.MASTER;
        const frequency = event.input?.frequency || event.frequency || 'Daily';

        // Validate trigger type
        const validTriggerTypes = Object.values(TRIGGER_TYPES);
        if (!validTriggerTypes.includes(triggerType)) {
            return {
                isValid: false,
                error: `Invalid trigger type: ${triggerType}. Valid types: ${validTriggerTypes.join(', ')}`
            };
        }

        return {
            isValid: true,
            triggerType,
            frequency
        };
    } catch (error) {
        return { isValid: false, error: `Input validation error: ${error.message}` };
    }
}

/**
 * Establishes database connection with proper error handling
 * @returns {Object|null} Database connection object or null if failed
 */
async function establishDatabaseConnection() {
    try {
        const requiredEnvVars = ['stageName', 'dbPrefix', 'dbSecretName', 'region'];
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

        if (missingVars.length > 0) {
            throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
        }

        const databaseConnection = await commonLib.stepFunctions.getConnection(
            process.env.stageName,
            process.env.dbPrefix,
            process.env.dbSecretName,
            process.env.region,
            '',
            1
        );

        if (!databaseConnection || !Object.keys(databaseConnection).length) {
            throw new Error('Database connection returned empty or invalid response');
        }

        return databaseConnection;
    } catch (error) {
        console.error('Failed to establish database connection:', error);
        return null;
    }
}

/**
 * Processes all organization instances for the given trigger type
 * @param {Array} orgCodeActiveInstances - Array of active organization instances
 * @param {Object} appmanagerDbConnection - App manager database connection
 * @param {string} triggerType - Type of trigger (Master, Employee, OnboardStatus)
 */
async function processOrganizationInstances(orgCodeActiveInstances, appmanagerDbConnection, triggerType) {
    const processingPromises = orgCodeActiveInstances.map(async (instance, index) => {
        let organizationDbConnection;

        try {
            const orgCode = instance['Org_Code'];
            const scheduleId = instance['Schedule_Id'];

            console.log(`Processing organization ${orgCode} (${index + 1}/${orgCodeActiveInstances.length})`);

            // Update status to InProgress
            await updateAPISchedule(appmanagerDbConnection, { Status: 'InProgress' }, scheduleId);

            // Establish organization database connection
            organizationDbConnection = await establishOrganizationDbConnection(orgCode);
            if (!organizationDbConnection) {
                throw new Error(`Failed to establish organization database connection for ${orgCode}`);
            }

            // Get authentication token
            const authentication = await getAuthenticationToken(organizationDbConnection);
            if (!authentication || !authentication.status) {
                throw new Error(`Authentication failed: ${authentication?.message || 'Unknown error'}`);
            }

            // Process based on trigger type
            await processByTriggerType(organizationDbConnection, authentication, triggerType, orgCode);

            // Update status to Success
            await updateAPISchedule(appmanagerDbConnection, {
                Status: 'Success',
                Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
            }, scheduleId);

            console.log(`Successfully processed organization ${orgCode}`);

        } catch (error) {
            console.error(`Error processing organization ${instance['Org_Code']}:`, error);

            // Log error to database
            if (organizationDbConnection) {
                await insertErrorLogs(organizationDbConnection, 'Organization Processing Error', error.message, null, { orgCode: instance['Org_Code'] });
            }

            // Update status to Failed
            await updateAPISchedule(appmanagerDbConnection, { Status: 'Failed' }, instance['Schedule_Id']);
        } finally {
            await closeDbConnection(organizationDbConnection, `Organization-${instance['Org_Code']}`);
        }
    });

    // Process all organizations concurrently with controlled concurrency
    await Promise.allSettled(processingPromises);
}

/**
 * Establishes organization database connection
 * @param {string} orgCode - Organization code
 * @returns {Object|null} Organization database connection or null if failed
 */
async function establishOrganizationDbConnection(orgCode) {
    try {
        const connection = await getConnection(
            process.env.stageName,
            process.env.dbPrefix,
            process.env.dbSecretName,
            process.env.region,
            orgCode
        );

        if (!connection || !connection.OrganizationDb) {
            throw new Error(`Invalid organization database connection for ${orgCode}`);
        }

        return knex(connection.OrganizationDb);
    } catch (error) {
        console.error(`Failed to establish organization database connection for ${orgCode}:`, error);
        return null;
    }
}

/**
 * Gets authentication token for PAGT API
 * @param {Object} organizationDbConnection - Organization database connection
 * @returns {Object} Authentication result
 */
async function getAuthenticationToken(organizationDbConnection) {
    try {
        const secretKeys = await commonLib.func.getCredentials(process.env.region, process.env.dbSecretName);
        const pagtNexusHrmsDNSURL = process.env.pagtAPIURL;

        if (!secretKeys || !secretKeys.pagt_apikey) {
            const errorMsg = 'Missing Secret Key in the secret manager for pagt_apikey object is unavailable';
            await insertErrorLogs(organizationDbConnection, 'PAGT Secret Key Missing', errorMsg, null, null);
            throw new Error(errorMsg);
        }

        if (!pagtNexusHrmsDNSURL) {
            throw new Error('PAGT API URL is not configured in environment variables');
        }

        return await getPagtAPIRequestToken(pagtNexusHrmsDNSURL, secretKeys);
    } catch (error) {
        console.error('Error getting authentication token:', error);
        throw error;
    }
}

/**
 * Processes synchronization based on trigger type
 * @param {Object} organizationDbConnection - Organization database connection
 * @param {Object} authentication - Authentication object
 * @param {string} triggerType - Type of trigger
 * @param {string} orgCode - Organization code
 */
async function processByTriggerType(organizationDbConnection, authentication, triggerType, orgCode) {
    const pagtNexusHrmsDNSURL = process.env.pagtAPIURL;

    try {
        switch (triggerType) {
            case TRIGGER_TYPES.MASTER:
                await processMasterSync(organizationDbConnection, authentication, pagtNexusHrmsDNSURL);
                break;
            case TRIGGER_TYPES.EMPLOYEE:
                await syncEmployeeDetails(organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode);
                break;
            case TRIGGER_TYPES.ONBOARD_STATUS:
                await pushToOnboardingStatusDetails(organizationDbConnection, authentication, pagtNexusHrmsDNSURL);
                break;
            default:
                throw new Error(`Unknown trigger type: ${triggerType}`);
        }
    } catch (error) {
        console.error(`Error in processByTriggerType for ${triggerType}:`, error);
        throw error;
    }
}

/**
 * Processes master data synchronization
 * @param {Object} organizationDbConnection - Organization database connection
 * @param {Object} authentication - Authentication object
 * @param {string} pagtNexusHrmsDNSURL - PAGT API base URL
 */
async function processMasterSync(organizationDbConnection, authentication, pagtNexusHrmsDNSURL) {
    try {
        // Fetch all master data concurrently
        const [departmentList, employeeTypeList, gradeList, designationList, locationList] = await Promise.all([
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + API_ENDPOINTS.ORGANIZATION_SETTINGS, authentication),
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + API_ENDPOINTS.EMPLOYEE_TYPES, authentication),
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + API_ENDPOINTS.GRADES, authentication),
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + API_ENDPOINTS.DESIGNATIONS, authentication),
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + API_ENDPOINTS.LOCATIONS, authentication),
        ]);

        // Validate API responses
        const apiResponses = [
            { name: 'departmentList', data: departmentList },
            { name: 'employeeTypeList', data: employeeTypeList },
            { name: 'gradeList', data: gradeList },
            { name: 'designationList', data: designationList },
            { name: 'locationList', data: locationList }
        ];

        for (const response of apiResponses) {
            if (!response.data) {
                console.warn(`Warning: ${response.name} returned empty or null data`);
            }
        }

        // Sync all master data concurrently
        await Promise.all([
            syncDepartmentDetails(organizationDbConnection, departmentList),
            syncDesignationDetails(organizationDbConnection, designationList),
            syncGradeDetails(organizationDbConnection, gradeList),
            syncEmployeeTypeDetails(organizationDbConnection, employeeTypeList),
            syncLocationDetails(organizationDbConnection, locationList),
        ]);

        console.log('Master data synchronization completed successfully');
    } catch (error) {
        console.error('Error in processMasterSync:', error);
        throw error;
    }
}

/**
 * Builds response object based on trigger type
 * @param {string} triggerType - Type of trigger
 * @param {string} message - Response message
 * @returns {Object} Response object
 */
function buildResponse(triggerType, message) {
    if (triggerType === TRIGGER_TYPES.MASTER) {
        return {
            nextStep: 'Step2',
            input: { source: TRIGGER_TYPES.EMPLOYEE, frequency: 'Daily' },
            message: 'Next Process will be to sync employee integration details.'
        };
    } else {
        return {
            nextStep: 'End',
            input: { source: '', frequency: '' },
            message: `Process completed on ${triggerType} integration details. ${message}`
        };
    }
}

/**
 * Builds error response object
 * @param {string} triggerType - Type of trigger
 * @param {string} errorMessage - Error message
 * @returns {Object} Error response object
 */
function buildErrorResponse(triggerType, errorMessage) {
    return {
        nextStep: 'End',
        input: { source: '' },
        message: `Error occurred while processing getPagtIntegrationDetails for ${triggerType || 'unknown'} sync details: ${errorMessage}`
    };
}

/**
 * Handles main function errors
 * @param {Error} error - Error object
 * @param {string} triggerType - Type of trigger
 * @param {Object} appmanagerDbConnection - App manager database connection
 * @param {Object} organizationDbConnection - Organization database connection
 */
async function handleMainError(error, triggerType, appmanagerDbConnection, organizationDbConnection) {
    try {
        console.error(`Main error handler - TriggerType: ${triggerType}, Error:`, error);

        // Additional error logging can be added here if needed
        // For example, logging to a centralized error tracking system

    } catch (handlerError) {
        console.error('Error in main error handler:', handlerError);
    }
}

/**
 * Safely closes database connection
 * @param {Object} connection - Database connection object
 * @param {string} connectionName - Name of the connection for logging
 */
async function closeDbConnection(connection, connectionName) {
    try {
        if (connection && typeof connection.destroy === 'function') {
            await connection.destroy();
            console.log(`${connectionName} database connection closed successfully`);
        }
    } catch (error) {
        console.error(`Error closing ${connectionName} database connection:`, error);
    }
}

/**
 * Gets PAGT API request token
 * @param {string} pagtNexusHrmsDNSURL - PAGT API base URL
 * @param {Object} paramsValue - Parameters containing API key
 * @returns {Object} Authentication result
 */
async function getPagtAPIRequestToken(pagtNexusHrmsDNSURL, paramsValue) {
    if (!pagtNexusHrmsDNSURL) {
        throw new Error('PAGT API URL is required');
    }

    if (!paramsValue || !paramsValue.pagt_apikey) {
        throw new Error('PAGT API key is required');
    }

    const url = `${pagtNexusHrmsDNSURL}/request-token?secretKey=${paramsValue.pagt_apikey}`;

    try {
        console.log("Requesting PAGT API token");

        const config = {
            method: 'post',
            url,
            maxBodyLength: Infinity,
            data: {},
            timeout: 30000, // 30 seconds timeout
        };

        console.log(`PAGT API token request URL: ${url}`);
        const { data } = await axios.request(config);

        if (!data) {
            throw new Error('Empty response from PAGT API token request');
        }

        console.log('PAGT API token request successful');
        return {
            status: true,
            ACCESS_TOKEN: data,
            TOKEN_TYPE: 'Bearer',
            message: 'Authentication successful'
        };
    } catch (error) {
        console.error('Error getting PAGT API token:', error.message);

        const { response } = error;
        let errorMessage = "Authentication error. Please verify the API key or token is valid and try again or contact your administrator.";

        if (response && response.data) {
            errorMessage = response.data?.MESSAGE || response.data?.Message || response.data?.message || errorMessage;
        } else if (error.code === 'ECONNABORTED') {
            errorMessage = "Request timeout. Please try again later.";
        } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
            errorMessage = "Unable to connect to PAGT API. Please check the API URL configuration.";
        }

        return { status: false, message: errorMessage };
    }
}

/**
 * Gets data from PAGT to Entomo API endpoints
 * @param {string} requestURL - API endpoint URL
 * @param {Object} authentication - Authentication object
 * @returns {Object|null} API response data or null if failed
 */
async function getPagtToEntomoAPIDetails(requestURL, authentication) {
    if (!requestURL) {
        console.error('Request URL is required');
        return null;
    }

    if (!authentication || !authentication.ACCESS_TOKEN) {
        console.error('Valid authentication is required');
        return null;
    }

    try {
        const config = {
            method: 'get',
            maxBodyLength: Infinity,
            url: requestURL,
            headers: {
                Authorization: `${authentication.TOKEN_TYPE} ${authentication.ACCESS_TOKEN}`,
                'Content-Type': 'application/json',
            },
            timeout: 30000 // 30 seconds timeout
        };

        console.log(`Making API request to: ${requestURL}`);
        const response = await axios.request(config);

        if (!response || !response.data) {
            console.warn(`Empty response from API: ${requestURL}`);
            return null;
        }

        console.log(`API request successful for: ${requestURL}, received ${Array.isArray(response.data) ? response.data.length : 'non-array'} items`);
        return response.data;

    } catch (error) {
        console.error(`Error calling API ${requestURL}:`, error.message);

        if (error.response) {
            console.error(`API Error Response:`, {
                status: error.response.status,
                statusText: error.response.statusText,
                data: error.response.data
            });
        }

        // Return null instead of undefined to make error handling more predictable
        return null;
    }
}


async function callingPagtAPI(url, authentication, data, type) {

    const config = {
        method: 'post',
        url,
        maxBodyLength: Infinity,
        data: data,
        headers: {
            Authorization: `${authentication.TOKEN_TYPE} ${authentication.ACCESS_TOKEN}`,
            'Content-Type': 'application/json',
        },
    };

    try {

        console.log("Inside callingPagtAPI type => "+type+" request => ", JSON.stringify(data));
       
        const { data: response } = await axios.request(config);
        console.log(`Inside callingExternalAPI ${type} success response => `, response);
        return { status: true, message: typeof response === "string" ? response : response.Message || response.toString() };
    } catch (error) {
        console.error(`Error callingExternalAPI ${type} function main catch block.`, error?.response?.data || error?.response || error);
       // console.log(`Inside callingExternalAPI ${type} request => `, config);
        const { status, statusText, data: { message: errorMessage, Message: errorMessage1 } = {} } = error.response || {};
        const errorDetails = `Error: ${status || ' Unknown Status Code '} - ${statusText || 'Unknown Status Text'}. Message: ${errorMessage || errorMessage1 || ' No error message provided'}`;
        return { status: false, message: errorDetails };
    }
}


async function syncEmployeeDetails(organizationDbConnection, authentication, pagtNexusHrmsDNSURL, orgCode) {

    try {

        console.log("Inside syncEmployeeDetails function");

        const [personalDetailList, additionalDetailList] = await Promise.all([
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + '/entomo/sync-employeeinfo-changes-to-entomo', authentication),
            getPagtToEntomoAPIDetails(pagtNexusHrmsDNSURL + '/entomo/sync-employeeAdditionalinfo-changes-to-entomo', authentication),
        ]);

        // Create a map from additionalDetailList for quick lookup
        const additionalDetailMap = Object.fromEntries(additionalDetailList.map(e => [e.EmployeeCode, e]));

        // Merge both lists, ensuring all records are included
        const employeeDetailList = [
            ...personalDetailList.map(emp => ({
                ...emp,
                ...(additionalDetailMap[emp.EmployeeCode] || {}),
                Type: 'Employee'
            })),
            ...additionalDetailList.filter(e => !personalDetailList.some(emp => emp.EmployeeCode === e.EmployeeCode)) // Add personal info missing records
        ];

        console.log('Inside syncEmployeeDetails function employeeDetailList => ', employeeDetailList);

        if(employeeDetailList && employeeDetailList.length) {

            const personalInfoMapping = [
                { key: 'LastName', value: 'Emp_Last_Name' },
                { key: 'NickName', value: 'Nick_Name' },
                { key: 'MaritalStatus', value: 'Marital_Status' },
                { key: 'PersonalMailID', value: 'Personal_Email' },
                { key: 'PhilHealthID', value: 'Statutory_Insurance_Number' },
                { key: 'HDMF_ID', value: 'PRAN_No' },
                { key: 'TIN', value: 'PAN' },
                { key: 'SSS_Number', value: 'UAN' },
            ];

            const contactInfoMapping = [
                { key: 'PermanentAddress', value: 'pApartment_Name' },
                { key: 'City', value: 'pCity' },
                { key: 'State', value: 'pState' },
                { key: 'Zipcode', value: 'pPincode' },
                { key: 'Barangay', value: 'pBarangay' },
                { key: 'Region', value: 'pRegion' },
                { key: 'CurrentAddress', value: 'cApartment_Name' },
                { key: 'CA_City', value: 'cCity' },
                { key: 'CA_State', value: 'cState' },
                { key: 'CA_Zipcode', value: 'cPincode' },
                { key: 'CA_Barangay', value: 'cBarangay' },
                { key: 'CA_Region', value: 'cRegion' },
                { key: 'MobileNo', value: 'Mobile_No' },
            ];

            let [departmentList, designationList, employeeTypeList, workLocationList, supervisorCodeList,
                serviceProviderList, maritalStatusList ] = await Promise.all([
                organizationDbConnection(ehrTables.department).select('Department_Id', 'Department_Code'),
                organizationDbConnection(ehrTables.designation).select('Designation_Id', 'Designation_Code'),
                organizationDbConnection(ehrTables.employeeType).select('EmpType_Id', 'Employee_Type_Code'),
                organizationDbConnection(ehrTables.location).select('Location_Id', 'Location_Code'),
                organizationDbConnection(ehrTables.empJob).select('Employee_Id', 'User_Defined_EmpId'),
                organizationDbConnection(ehrTables.serviceProvider).select('Service_Provider_Id', 'Service_Provider_Code'),
                organizationDbConnection(ehrTables.maritalStatus).select('Marital_Status_Id', 'Marital_Status'),
            ]);

            departmentList = departmentList.reduce((acc, row) => { acc[row.Department_Code.toString().toLowerCase()] = row;  return acc; }, {});
            designationList = designationList.reduce((acc, row) => { acc[row.Designation_Code.toString().toLowerCase()] = row;  return acc; }, {});
            employeeTypeList = employeeTypeList.reduce((acc, row) => { acc[row.Employee_Type_Code.toString().toLowerCase()] = row;  return acc; }, {});
            workLocationList = workLocationList.reduce((acc, row) => { acc[row.Location_Code.toString().toLowerCase()] = row;  return acc;}, {});
            supervisorCodeList = supervisorCodeList.reduce((acc, row) => { acc[row.User_Defined_EmpId.toString().toLowerCase()] = row;  return acc;}, {});
            serviceProviderList = serviceProviderList.reduce((acc, row) => { acc[row.Service_Provider_Code.toString().toLowerCase()] = row;  return acc;}, {});
            maritalStatusList = maritalStatusList.reduce((acc, row) => { acc[row.Marital_Status.toString().toLowerCase()] = row;  return acc;}, {});
    

            let employeeJobList = await organizationDbConnection(ehrTables.empJob)
            .select('Employee_Id', 'Designation_Id', 'Department_Id', 'EmpType_Id', 'Date_Of_Join', 'User_Defined_EmpId')
            const employeeJobMap = employeeJobList.reduce((acc, row) => {acc[row.User_Defined_EmpId] = row; return acc;}, {});

            let dateOfJoinChangedEmployees = [], customGroupRefreshEmployees = [], empJobUpdateInfoList = [], entomoSyncEmployeeIds = [];


            for(let i=0; i<employeeDetailList.length; i++){

                const { EmployeeCode, ...employeeDetails } = employeeDetailList[i];
                let trx = await organizationDbConnection.transaction()

                try {

                    let employeeJobs = employeeJobMap[EmployeeCode];
                    if (!employeeJobs) {
                        throw `${EmployeeCode} The requested employee details could not be found in the system.`;
                    }
                    let employeeId = employeeJobs.Employee_Id;


                    if(employeeDetails && employeeDetails.Type === 'Employee'){

                        let personalInfo = convertObjectKeyToAnotherKeyValue(employeeDetails, personalInfoMapping);
                        if(personalInfo.Marital_Status && !maritalStatusList[personalInfo?.Marital_Status.toLowerCase()]){
                            throw `The requested employee details could not be found in the system Marital Status (${personalInfo?.Marital_Status?.toLowerCase()}): ${maritalStatusList[personalInfo.Marital_Status.toLowerCase()] ? 'Available' : 'Not Available'}`;
                        }
                        personalInfo.Marital_Status = maritalStatusList[personalInfo?.Marital_Status?.toLowerCase()]?.Marital_Status_Id || null;

                        await updatePersonalInfo(organizationDbConnection, trx,  personalInfo, employeeId);
                        
                        if (moment(employeeDetails.DateOfJoin, 'YYYY-MM-DD HH:mm:ss').format('YYYY-MM-DD') !== moment(employeeJobs.Date_Of_Join).format('YYYY-MM-DD')) {
                            dateOfJoinChangedEmployees.push(employeeId);
                        } 

                        const department = departmentList[employeeDetails?.DepartmentCode?.toString().toLowerCase()];
                        const designation = designationList[employeeDetails?.DesignationCode?.toString().toLowerCase()];
                        const employeeType = employeeTypeList[employeeDetails?.EmployeeTypeCode?.toString().toLowerCase()];
                        const workLocation = workLocationList[employeeDetails?.WorkLocationCode?.toString().toLowerCase()];
                        const supervisorCode = supervisorCodeList[employeeDetails?.SupervisorCode?.toString().toLowerCase()];
                        const serviceProvider = serviceProviderList[employeeDetails?.OrganizationUnit?.toString().toLowerCase()];
                       
                        const missingFields = [designation, department, employeeType, workLocation, 
                            supervisorCode, serviceProvider].some(field => !field);
                
                        if (missingFields) {
                            throw `The requested employee details could not be found in the database. 
                            Department_Code (${employeeDetails?.DepartmentCode?.toString().toLowerCase()}): ${designation?.Designation_Id ? 'Available' : 'Not Available'}, 
                            Designation_Code (${employeeDetails?.DesignationCode?.toString().toLowerCase()}): ${department?.Department_Id ? 'Available' : 'Not Available'}, 
                            Employee_Type_Code (${employeeDetails?.EmployeeTypeCode?.toString().toLowerCase()}): ${employeeType?.EmpType_Id ? 'Available' : 'Not Available'}, 
                            Location_Code (${employeeDetails?.WorkLocationCode?.toString().toLowerCase()}): ${workLocation?.Location_Id ? 'Available' : 'Not Available'},
                            OrganizationUnit (${employeeDetails?.OrganizationUnit?.toString().toLowerCase()}): ${serviceProvider?.Service_Provider_Id ? 'Available' : 'Not Available'}`;
                        }
                
                        if (designation.Designation_Id !== employeeJobs.Designation_Id || department.Department_Id !== employeeJobs.Department_Id || employeeType.EmpType_Id !== employeeJobs.EmpType_Id) {
                            customGroupRefreshEmployees.push(employeeId);
                        }

                       const jobInfo = {
                            ...(designation && {Designation_Id: designation.Designation_Id}),
                            ...(department && {Department_Id: department.Department_Id}),
                            ...(workLocation && {Location_Id: workLocation.Location_Id}),
                            ...(employeeType && {EmpType_Id: employeeType.EmpType_Id}),
                            ...(supervisorCode && {Manager_Id: supervisorCode.Employee_Id}),
                            ...(employeeDetails.ConfirmationDate && {Confirmation_Date: employeeDetails.ConfirmationDate}),
                            ...(serviceProvider && {Service_Provider_Id: serviceProvider.Service_Provider_Id}),
                            ...(employeeDetails.WorkEmail && {Emp_Email: employeeDetails.WorkEmail}),
                          
                            Date_Of_Join: moment(employeeDetails.DateOfJoin).format('YYYY-MM-DD'),
                            ...(employeeDetails.Status && {Emp_Status: employeeDetails.Status.toLowerCase() === 'inactive' ? 'InActive' : 'Active'}),
                            ...(employeeDetails.Inactive_Date && {Emp_InActive_Date: moment( employeeDetails.Inactive_Date).format('YYYY-MM-DD')})
                        };

                        empJobUpdateInfoList.push(organizationDbConnection(ehrTables.empJob).transacting(trx)
                        .update(jobInfo).where('Employee_Id', employeeId));

                      
                        let contactData = convertObjectKeyToAnotherKeyValue(employeeDetails, contactInfoMapping);
                        contactData.pApartment_Name = contactData?.pApartment_Name?.split(",")[0] || contactData?.pApartment_Name;
                        contactData.cApartment_Name = contactData?.cApartment_Name?.split(",")[0] || contactData?.cApartment_Name;
                        if(contactData.Mobile_No){
                            const mobileNo = contactData.Mobile_No.split(" ");
                            contactData.Mobile_No_Country_Code = mobileNo.length === 2 ? mobileNo[0] : null;
                            contactData.Mobile_No = mobileNo.length === 2 ? mobileNo[1] : mobileNo[0];
                        }

                        await updateContactInfo(organizationDbConnection, trx,  contactData, employeeId);

                        await updateCustomFieldValues(organizationDbConnection, trx, employeeDetails, employeeId);

                    } else {

                        const personalInfo = convertObjectKeyToAnotherKeyValue(employeeDetails, personalInfoMapping);
                        await updatePersonalInfo(organizationDbConnection, trx,  personalInfo, employeeId);


                        const updateJobData = {
                            ...(employeeDetails.WorkEmail && { Emp_Email: employeeDetails.WorkEmail }),
                            ...(employeeDetails.Status && {
                              Emp_Status: employeeDetails.Status.toLowerCase() === 'inactive' ? 'InActive' : 'Active',
                            }),
                            ...(employeeDetails.Inactive_Date && {
                              Emp_InActive_Date: moment(employeeDetails.Inactive_Date).format('YYYY-MM-DD'),
                            }),
                        };

                        if (Object.keys(updateJobData).length > 0) {
                            empJobUpdateInfoList.push(organizationDbConnection(ehrTables.empJob).transacting(trx)
                            .update(updateJobData).where('Employee_Id', employeeId));
                        }

                        await updateCustomFieldValues(organizationDbConnection, trx, employeeDetails, employeeId);
                    }

                    entomoSyncEmployeeIds.push(employeeId);

                    await trx.commit();

                } catch (error) {
                    console.error("Error while syncEmployeeDetails catch block => ", error);
                    await trx.rollback();
                    delete employeeDetails.Type;

                    await insertErrorLogs(organizationDbConnection, 'Sync Employee Details', error, EmployeeCode, JSON.stringify(employeeDetails));
                }
            }

            empJobUpdateInfoList.length ? await Promise.all(empJobUpdateInfoList) : null;
            await pushToEntomoSyncStatus(organizationDbConnection, entomoSyncEmployeeIds);
            await initiateCustomGroupRefresh(orgCode, customGroupRefreshEmployees, dateOfJoinChangedEmployees);
        }

    } catch (error) {
        console.error("Error while syncEmployeeDetails function main catch block => ", error);
        throw error;
    }
}


async function updateJobInfo(organizationDbConnection, trx, employeeInfo, employeeId, employeeJob, customGroupRefreshEmployees) {

    try {
        console.log("Inside updateJobInfo function");
        const [department, designation, employeeType, workLocation, supervisorCode,
            businessUnit, organizationGroup, serviceProvider ] = await Promise.all([
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.department, 'Department_Id', {Department_Code: employeeInfo.DepartmentCode}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.designation, 'Designation_Id', {Designation_Code: employeeInfo.DesignationCode}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.employeeType, 'EmpType_Id', {Employee_Type_Code: employeeInfo.EmployeeTypeCode}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.location, 'Location_Id', {Location_Code: employeeInfo.WorkLocationCode}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.empJob, 'Employee_Id', {User_Defined_EmpId: employeeInfo.SupervisorCode}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.businessUnit, 'Business_Unit_Id', {Business_Unit_Code: employeeInfo.BusinessUnit}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.organizationGroup, 'Organization_Group_Id', {Organization_Group_Code: employeeInfo.OrganizationGroup}),
            getPrimaryIdBasedonCode(organizationDbConnection, ehrTables.serviceProvider, 'Service_Provider_Id', {Service_Provider_Code: employeeInfo.OrganizationUnit})
        ]);

        const missingFields = [designation, department, employeeType, workLocation, 
            supervisorCode, businessUnit, organizationGroup, serviceProvider].some(field => !field);

        if (missingFields) {
            throw `The requested employee details could not be found in the database. 
            Department_Code (${employeeInfo.DepartmentCode}): ${designation?.Designation_Id ? 'Available' : 'Not Available'}, 
            Designation_Code (${employeeInfo.DesignationCode}): ${department?.Department_Id ? 'Available' : 'Not Available'}, 
            Employee_Type_Code (${employeeInfo.EmployeeTypeCode}): ${employeeType?.EmpType_Id ? 'Available' : 'Not Available'}, 
            Location_Code (${employeeInfo.WorkLocationCode}): ${workLocation?.Location_Id ? 'Available' : 'Not Available'},
            Business_Unit_Code (${employeeInfo.BusinessUnit}): ${businessUnit?.Business_Unit_Id ? 'Available' : 'Not Available'}, 
            Organization_Group_Code (${employeeInfo.OrganizationGroup}): ${organizationGroup?.Organization_Group_Id ? 'Available' : 'Not Available'},
            OrganizationUnit (${employeeInfo.OrganizationUnit}): ${serviceProvider?.Service_Provider_Id ? 'Available' : 'Not Available'}`;
        }

        if (designation.Designation_Id !== employeeJob.Designation_Id || department.Department_Id !== employeeJob.Department_Id || employeeType.EmpType_Id !== employeeJob.EmpType_Id) {
            customGroupRefreshEmployees.push(employeeId);
        }

        const jobInfo  = {
            ...(designation && {Designation_Id: designation.Designation_Id}),
            ...(department && {Department_Id: department.Department_Id}),
            ...(workLocation && {Location_Id: workLocation.Location_Id}),
            ...(employeeType && {EmpType_Id: employeeType.EmpType_Id}),
            ...(supervisorCode && {Manager_Id: supervisorCode.Employee_Id}),
            ...(employeeInfo.ConfirmationDate && {Confirmation_Date: employeeInfo.ConfirmationDate}),
            ...(businessUnit && {Business_Unit_Id: businessUnit.Business_Unit_Id}),
            ...(organizationGroup && {Organization_Group_Id: organizationGroup.Organization_Group_Id}),
            ...(serviceProvider && {Service_Provider_Id: serviceProvider.Service_Provider_Id}),
            ...(employeeInfo.WorkEmail && {Emp_Email: employeeInfo.WorkEmail}),
          
            Date_Of_Join: employeeInfo.DateOfJoin,
            Emp_Status: employeeInfo.Status === 'Active' ? 'Active' : 'InActive',
            ...(employeeInfo.Inactive_Date && {Emp_InActive_Date: employeeInfo.Inactive_Date})
        }

        return organizationDbConnection(ehrTables.empJob).transacting(trx)
        .update(jobInfo).where('Employee_Id', employeeId)

    } catch (error) {
        console.error("Error while updateJobInfo function main catch block => ", error);
        throw error;
    }
}


async function updateCustomFieldValues(organizationDbConnection, trx, employeeDetails, employeeId) {

    try {
        console.log("Inside updateCustomFieldValues function");
        const [customValues, customFieldAssociated] = await Promise.all([
            organizationDbConnection('team_summary_custom_field_values as TSCV').transacting(trx)
            .select('TSCV.Custom_Field_Value').whereNotNull('TSCV.Custom_Field_Value')
            .where('TSCV.Primary_Id', employeeId).first(),
            
            organizationDbConnection('custom_field_associated_forms as CFAF').transacting(trx)
            .select('CFAF.Integration_Mapping_Key', 'CFAF.Custom_Field_Id', 'CF.Custom_Field_Type')
            .innerJoin('custom_fields as CF', 'CFAF.Custom_Field_Id', 'CF.Custom_Field_Id')
            .where('CFAF.Form_Id', '243')
            .whereIn('CFAF.Integration_Mapping_Key', ['IsPWD', 'IsCPA', 'PWDID'])
        ]);

        let parsedValues = customValues ? JSON.parse(customValues?.Custom_Field_Value || '{}') : {}

        if(customFieldAssociated && customFieldAssociated.length > 0){
            customFieldAssociated.forEach(customField => {
                if(parsedValues[customField.Custom_Field_Id] && employeeDetails[customField.Integration_Mapping_Key]){
                    if(customField.Custom_Field_Type && customField.Custom_Field_Type.toLowerCase() === 'single choice'){
                        parsedValues[customField.Custom_Field_Id] = employeeDetails[customField.Integration_Mapping_Key] ? 'Yes' : 'No';
                    } else {
                        parsedValues[customField.Custom_Field_Id] = employeeDetails[customField.Integration_Mapping_Key];
                    }
                }
            })
        }

        return organizationDbConnection('team_summary_custom_field_values').transacting(trx)
        .update({Custom_Field_Value: JSON.stringify(parsedValues)})
        .where('Primary_Id', employeeId);

    } catch (error) {
        console.error("Error while updateCustomFieldValues function main catch block => ", error);
        throw error;
    }
}



async function getPrimaryIdBasedonCode(organizationDbConnection, tableName, fieldName, whereCondition){

    try {
        return await organizationDbConnection(tableName)
        .select(fieldName).first().where(whereCondition);

    } catch (error) {
        console.error("Error while getPrimaryIdBasedonCode main catch block => ", error);
        throw error;
    }
}

async function getOrgActiveInsances(appmanagerDbConnection, triggerType) {
    try{

        await appmanagerDbConnection('api_integration_schedule as AIS')
        .update({
            Status: 'Open',
            Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss')
        }).where('Data_Push_Pull', 'Pull')
        .andWhere('Integration_Type','pagt').andWhere('Trigger_Type', triggerType);

        const data = await appmanagerDbConnection('api_integration_schedule')
        .distinct('Org_Code', 'Schedule_Id ').where('Data_Push_Pull', 'Pull')
        .andWhere('Integration_Type', 'pagt').whereIn('Status', ['Open'])
        .andWhere('Trigger_Type', triggerType);

        return data;
    }
    catch(e){
        console.log("Error in getOrgActiveInsances function main catch block.",e);
        return [];
    }
}

async function updateAPISchedule(appmanagerDbConnection, updateparams, schedulerId) {

    try {
        await appmanagerDbConnection('api_integration_schedule').update(updateparams).where('Schedule_Id', schedulerId);
    } catch (error) {
        console.error("Error while updateAPISchedule function main catch block => ", error);
        throw error;
    }
}


async function pushToEntomoSyncStatus(organizationDbConnection, employeeIds) {

    try {
        console.log("Inside pushToEntomoSyncStatus function");
        let entomoFormedEmployeeList = await organizationDbConnection(ehrTables.empPersonalInfo + ' as EP')
        .select('EJ.Employee_Id as employeeId','EP.Emp_First_Name as firstName', 'EP.Emp_Last_Name as lastName', 'EJ.Emp_Email as email', 'EJ.Emp_Email as username', 'EP.DOB',
            'EJ.User_Defined_EmpId as code', 'EJ.Date_Of_Join as joiningDate', 'EJ.Designation_Id as designationId', 'EJ.EmpType_Id',
            'DES.Designation_Code', 'ET.Employee_Type_Code', 'JG.Grade_Code', 'EJ.Manager_Id', 'BU.Business_Unit_Code', 'EPJ.User_Defined_EmpId as primarySupervisorId',
            organizationDbConnection.raw("CONCAT_WS(' ',EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as displayName"))
        .innerJoin(ehrTables.empJob + ' as EJ', 'EJ.Employee_Id', 'EP.Employee_Id')
        .innerJoin(ehrTables.designation + ' as DES', 'DES.Designation_Id', 'EJ.Designation_Id')
        .innerJoin(ehrTables.empGrade + ' as JG', 'JG.Grade_Id', 'DES.Grade_Id')
        .innerJoin(ehrTables.employeeType + ' as ET', 'ET.EmpType_Id', 'EJ.EmpType_Id')
        .leftJoin(ehrTables.businessUnit + ' as BU', 'BU.Business_Unit_Id', 'EJ.Business_Unit_Id')
        .leftJoin(ehrTables.empJob + ' as EPJ', 'EJ.Manager_Id', 'EPJ.Employee_Id')
        .whereIn('EJ.Employee_Id', employeeIds)

        if(entomoFormedEmployeeList && entomoFormedEmployeeList.length > 0){

            const entomoEmployeeList = entomoFormedEmployeeList.map(entomoFormedEmployee => {
                
                let employeeId = entomoFormedEmployee.employeeId;
                delete entomoFormedEmployee.employeeId;
            
                let entomoEmployee = {
                    "displayName": {
                        "en": entomoFormedEmployee.displayName
                    },
                    "email": entomoFormedEmployee.email,
                    "username": entomoFormedEmployee.email,
                    "firstName": entomoFormedEmployee.firstName,
                    "lastName": entomoFormedEmployee.lastName,
                    "code": entomoFormedEmployee.code,
                    "joiningDate": entomoFormedEmployee.joiningDate,
                    "organizationUnitId": null,
                    "jobGradeId": entomoFormedEmployee.Grade_Code,
                    "designationId": entomoFormedEmployee.Designation_Code,
                    "employmentTypeId": entomoFormedEmployee.Employee_Type_Code,
                    "businessUnitId": entomoFormedEmployee.Business_Unit_Code,
                    "jobCategoryId": null,
                    "positionId": null,
                    "payBasisId": null,
                    "nationalityGroupId": null,
                    "staffCategoryId": null,
                    "primarySupervisorId": entomoFormedEmployee.primarySupervisorId,
                    "dateOfBirth": entomoFormedEmployee.DOB
                }

                return {
                    Status: 'Open', 
                    Action: 'Update', 
                    Entity_Type: 'Employee',
                    Entity_Id: employeeId,
                    Form_Data: entomoEmployee,
                    Integration_Type: 'Entomo',
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1
                }
            });
            
            await organizationDbConnection('external_api_sync_status').insert(entomoEmployeeList)
        }

        return false;
    } catch (error) {
        console.error("Error in pushToEntomoSyncStatus function main catch block.",error);
        return false;
    }
}

async function pushToOnboardingStatusDetails(organizationDbConnection, authentication, pagtNexusHrmsDNSURL) {

    try {

        console.log("Inside pushToOnboardingStatusDetails function");

        let subQuery = organizationDbConnection(ehrTables.candidateRecruitmentInfo + ' as CRI1')
                    .count('CRI1.Candidate_Status')
                    .leftJoin(ehrTables.atsStatusTable + ' as AST', 'AST.Id', 'CRI1.Candidate_Status')
                    .whereRaw('CRI1.Job_Post_Id = JP.Job_Post_Id')
                    .where('AST.Status', 'Hired')
                   
        const onboardingStatusList = await organizationDbConnection(ehrTables.candidateRecruitmentInfo + ' as CRI')
            .distinct().select('JP.Job_Post_Id as RoleID', 'JP.Job_Post_Name as RoleName', 'JP.No_Of_Vacancies as AvailableSlots',
            subQuery.clone().as('OccupiedSlots'), organizationDbConnection.raw('JP.No_Of_Vacancies - ? as RemainingSlots', [subQuery.clone()]),
            organizationDbConnection.raw("CASE WHEN L.Location_Code IS NOT NULL THEN L.Location_Code ELSE '' END as LocationCode"),
            organizationDbConnection.raw("CASE WHEN D.Department_Code IS NOT NULL THEN D.Department_Code ELSE '' END as DepartmentCode"),
            organizationDbConnection.raw('\'\' AS DivisionCode'),
            organizationDbConnection.raw('\'\' AS SectionCode'),
            organizationDbConnection.raw('\'\' AS DesignationCode'),
            organizationDbConnection.raw('CASE WHEN AS.Status IS NOT NULL AND AS.Status = "Open" THEN 1 ELSE 0 END AS IsClosed'),
        )
        .innerJoin(ehrTables.jobPost + ' as JP', 'JP.Job_Post_Id', 'CRI.Job_Post_Id')
        .leftJoin(ehrTables.jobPostLocation + ' as JPL', 'JPL.Job_Post_Id', 'JP.Job_Post_Id')
        .leftJoin(ehrTables.location + ' as L', 'L.Location_Id', 'JPL.Location_Id')
        .leftJoin(ehrTables.department + ' as D', 'D.Department_Id', 'JP.Functional_Area')
        .leftJoin(ehrTables.atsStatusTable + ' as AS', 'AS.Id', 'JP.Status')
       
        if(onboardingStatusList && onboardingStatusList.length > 0){
            for(let onboardingStatus of onboardingStatusList){
                const response = await callingPagtAPI(`${pagtNexusHrmsDNSURL}/entomo/sync-onboarding-status-details-to-staging`, authentication, [onboardingStatus], "OnboardingStatus");
                if(response && !response.status){
                    await insertErrorLogs(organizationDbConnection, 'PAGT Push to Onboarding Status API Error', response.message, null, [onboardingStatus]);
                }
            }
        }


    } catch (error) {    
        console.error('Error pushToOnboardingStatusDetails() function main catch block.', error);
        throw error;
    }
}


async function syncDepartmentDetails(organizationDbConnection, organizationSettingList) {
    try {
        console.log("Inside syncDepartmentDetails function => ");
        if (organizationSettingList && organizationSettingList.length > 0) {

            const divisionList = organizationSettingList.filter(department => department.Level && department.Level === 1);
            const departmentList = organizationSettingList.filter(department => department.Level && department.Level === 2);
            const sectionList = organizationSettingList.filter(department => department.Level && department.Level === 3);
            
            const invalidLevelList = organizationSettingList.filter(department => !department.Level);
            if(invalidLevelList && invalidLevelList.length > 0){
                await insertErrorLogs(organizationDbConnection, 'Sync Department List To Entomo', 'Unable to find the level for the given list.', null, invalidLevelList);
            }
           
            const processOrganizationUnits = async (organizationDbConnection, unitList, orgStructure) => {
                const result = await Promise.all(unitList.map(async unit => {
 
                    const parentUnit = unit.ParentOrgCode ? await organizationDbConnection(ehrTables.department)
                            .select('Department_Id', 'Parent_Type_Id', 'Department_Hierarchy')
                            .where('Department_Code', unit.ParentOrgCode).first() : null;

                    return {
                        Department_Name: unit.OrgName,
                        Department_Code: unit.OrgCode,
                        Parent_Type_Id: parentUnit ? parentUnit.Department_Id : 0,
                        Department_Hierarchy: parentUnit ? `${parentUnit.Department_Hierarchy},${parentUnit.Department_Id}` : '0',
                        Department_Status: "Active",
                        Description: unit.Description,
                        Organization_Type_Id: orgStructure?.Org_Structure_Id || 1,
                        Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                        Added_By: 1,
                        Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                        Updated_By: 1
                    }
                }));

                result.length ? await organizationDbConnection(ehrTables.department)
                .insert(result).onConflict('Department_Code').merge() : null;
            };

            const [divOrgStructure, deptOrgStructure, secOrgStructure] = await Promise.all([
                getPrimaryIdBasedonCode(organizationDbConnection, 'org_structure', 'Org_Structure_Id', {'Level': 1} ),
                getPrimaryIdBasedonCode(organizationDbConnection, 'org_structure', 'Org_Structure_Id', {'Level': 2} ),
                getPrimaryIdBasedonCode(organizationDbConnection, 'org_structure', 'Org_Structure_Id', {'Level': 3} )
            ])

            // Process Divisions, Departments, and Sections
           await processOrganizationUnits(organizationDbConnection, divisionList, divOrgStructure);
           await processOrganizationUnits(organizationDbConnection, departmentList, deptOrgStructure);
           await processOrganizationUnits(organizationDbConnection, sectionList, secOrgStructure);
        }
    } catch (error) {
        console.error('Error syncDepartmentDetails() function main catch block.', error);
        throw error;
    }
}

async function syncEmployeeTypeDetails(organizationDbConnection, employeeTypeList) {

    try {
        console.log("Inside syncEmployeeTypeDetails function");
        if (employeeTypeList && employeeTypeList.length > 0) {
            const employeeTypePromiseList = employeeTypeList.map(employeeType => {
                return {
                    Employee_Type: employeeType.EmployeeType,
                    Employee_Type_Code: employeeType.CategoryCode,
                    Holiday_Eligiblity: employeeType.HolidayEligibility,
                    Work_Schedule: employeeType.WorkSchedule || 'Employee Level',
                    EmployeeType_Status: "Active",
                    Salary_Calc_Days: 0,
                    Fixed_Days: 0,
                    Comp_Off_Days: 0,
                    Comp_Off_Fixed_Days: 0,
                    Display_Total_Hours_In_Minutes: 0,
                    Exclude_Break_Hours: 0,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Updated_By: 1
                }
            });

            employeeTypePromiseList.length ? await organizationDbConnection(ehrTables.employeeType)
            .insert(employeeTypePromiseList).onConflict('Employee_Type_Code').merge() : null;
            
        }
    } catch (error) {
        console.error('Error syncEmployeeTypeDetails() function main catch block.', error);
        throw error;
    }
}


async function syncDesignationDetails(organizationDbConnection, designationList) {

    try {
        console.log("Inside syncDesignationDetails function");
        if (designationList && designationList.length > 0) {
            const designationPromiseList = designationList.map(designation => {
                return {
                    Designation_Name: designation.DesignationName,
                    Designation_Code: designation.DesignationCode,
                    Parent_Org_Unit_Code: designation.ParentOrgUnitCode,
                    Grade_Id: designation.Grade || 1,
                    Probation_Days: designation.ProbationDays || 0,
                    Designation_Status: "Active",
                    Employee_Confirmation: designation.EmployeeConfirmation || 'Manual',
                    Notice_Period_Days_Within_Probation: designation.NoticePeriodDuringProbation || 0,
                    Notice_Period_Days_After_Probation: designation.NoticePeriodAfterProbation || 0,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Updated_By: 1
                }
            });
            
            designationPromiseList ? await organizationDbConnection(ehrTables.designation)
            .insert(designationPromiseList).onConflict('Designation_Code').merge() : null;

        }
    } catch (error) {
        console.error('Error syncDesignaionDetails() function main catch block.', error);
        throw error;
    }
}


async function syncGradeDetails(organizationDbConnection, gradeList) {
    try {
        console.log("Inside syncGradeDetails function");
        if (gradeList && gradeList.length > 0) {

            let parentGradeList = await  organizationDbConnection(ehrTables.empGrade).select('Grade_Id', 'Grade_Code');
            parentGradeList = parentGradeList.reduce((acc, row) => { acc[row.Grade_Code] = row;  return acc;}, {});

            const gradePromiseList = await Promise.all(gradeList.map(grade => {
                const parentGrade = parentGradeList[grade.ParentGrade] || null;
                return {
                    Grade: grade.EmployeeGrade,
                    Grade_Code: grade.GradeCode,
                    ParentGrade_Id: parentGrade ? parentGrade.Grade_Id : null,
                    Min_AnnualSalary: 0,
                    Max_AnnualSalary: 0,
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1,
                    Updated_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Updated_By: 1
                }
            }));

            gradePromiseList.length  ? await organizationDbConnection(ehrTables.empGrade)
            .insert(gradePromiseList).onConflict('Grade_Code').merge() : null;

        }
    } catch (error) {
        console.error('Error syncGradeDetails() function main catch block.', error);
        throw error;
    }
}

async function syncLocationDetails(organizationDbConnection, locationList) {
    try {
        console.log("Inside syncLocationDetails function");
        if (locationList && locationList.length > 0) {

            let locationCityList = await organizationDbConnection(ehrTables.city + ' as c').select('c.City_Id', 'c.City_Name', 's.State_Id', 's.Country_Code')
            .innerJoin(ehrTables.state + ' as s', 's.State_Id', 'c.State_Id');

            locationCityList = locationCityList.reduce((acc, row) => { acc[row.City_Name] = row;  return acc;}, {});

            let locationPromiseList = [], validationErrors = [];
            for (let location of locationList) {
    
                const locationCity = locationCityList[location.City]; 
                if(!locationCity){
                    validationErrors.push(location);
                    continue;
                }
                locationPromiseList.push({
                    Location_Name: location.LocationName,
                    Location_Code: location.LocationCode,
                    Location_Type: location.LocationType,
                    Street1: location.Address,
                    City_Id: locationCity.City_Id,
                    State_Id: locationCity.State_Id,
                    Country_Code: locationCity.Country_Code,
                    Pincode: location.ZipCode,
                    Currency_Symbol: location.CurrencyType,
                    Phone: location.ContactNumber,
                    Location_Status: 'Active',
                    Added_On: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
                    Added_By: 1
                });
            }
            
            locationPromiseList.length ? await organizationDbConnection(ehrTables.location)
            .insert(locationPromiseList).onConflict('Location_Code').merge() : null;

            validationErrors.length ? await insertErrorLogs(organizationDbConnection, 'Sync Location List To Entomo', 'Unable to find the specified state or city for the given location.', null, {data: validationErrors}) : null;

        }
    } catch (error) {
        console.error('Error syncLocationDetails() function main catch block.', error);
        throw error;
    }
}


async function insertErrorLogs(organizationDbConnection, errorType, error, employeeId, data){

    try {
        console.log('Inside insertErrorLogs function');
        return organizationDbConnection(ehrTables.dataIntegrationFailureLogs)
        .insert({
            Integration_Type: 'PAGT Nexus HRMS To Entomo',
            Entity_Type: errorType,
            Failure_Reason: error?.message?.toString() || error.toString(),
            ...(employeeId && {Primary_Id: employeeId}),
            ...(data && {Failure_Data: data}),
            Integration_Date: moment().utc().format('YYYY-MM-DD HH:mm:ss'),
        });
    } catch (error) {
        console.error('Error insertErrorLogs() function main catch block.', error);
    }
}




